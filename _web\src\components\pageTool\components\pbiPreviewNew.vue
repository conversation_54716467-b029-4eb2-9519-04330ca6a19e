<template>

  <div @contextmenu.prevent="preventRightClick" style="z-index: 999">
    <a-drawer

      :visible="filePreviewVisible" :bodyStyle="{ height: '100%' }" :width="width?width:'70%'" :closable="true"
      placement="right"
      destroy-on-close
      :maskClosable="true"
      @close="handleClose">
      <div style="font-size: 16px;font-weight: bolder;margin-bottom: 8px;" v-if="false">
        {{ fileName ? fileName : fileInfo.fileOriginName }}
      </div>
      <a-spin tip="加载中" :spinning="spinning" v-if="spinning" style="height: 100%;width: 100%;align-content: center;">
      </a-spin>
<!--      <div class="pdf-container" @contextmenu.prevent v-else-if="isPdf(fileSuffix)">
        <pdf
          v-for="page in numPages"
          :key="page"
          :src="pdfDocument"
          :page="page"
          class="pdf-page"
        ></pdf>
      </div>-->

      <iframe @contextmenu.prevent v-if="isPdf(fileSuffix)" :src="previewUrl" width="100%" height="100%"></iframe>
      <iframe @contextmenu.prevent v-else-if="isTXT(fileSuffix)" :src="previewUrl" width="100%" height="100%"></iframe>
      <img @contextmenu.prevent v-else-if="isImg(fileSuffix)" width="100%" height="100%" :src="previewUrl" alt="">
      <video v-else-if="isMp4(fileSuffix)" width="100%" height="100%" ref="videoPlayer"
             autoplay
             controls
             playsinline>
        <source :src="previewUrl" type="video/mp4">
      </video>
    </a-drawer>
  </div>



</template>
<script>
import pdf from 'vue-pdf'
import Vue from "vue";
import {getMinioDownloadUrl, getMinioPreviewUrl, sysFileInfoDetail,convertToPdf} from "@/api/modular/system/fileManage";
import {downloadMinioFile} from "@/utils/util";
import { debounce } from 'lodash';

export default {
  components:{
    pdf
  },
  props: {
    width: {   // drawer 宽度
      type: String,
      default: null
    },
    showFileName: {
      type: Boolean,
      default: true
    }

  },
  /*watch: {
    fileId(newVal, oldVal) {
      this.init()
    },
    previewFileUrl(newVal, oldVal) {
      this.init()
    }
  },*/
  data() {
    return {
      filePreviewVisible: false,
      previewUrl: '',
      fileInfo: {},
      fileId: null,
      fileName: null,
      previewFileUrl: null,
      canNotDownload: false,
      numPages:0,
      pdfDocument:null,
      convertNum:0,
      fileSuffix:'',
      spinning:false

    }
  },
  created() {
    this.init()
  },

  mounted() {


  },
  beforeDestroy() {
    /*if (this.loadingTask) {
      this.loadingTask.destroy();
    }*/
  },
  methods: {

    //pbi文件必须传递文件id
    //有fileName传递则展示fileName，无则展示上传时文件名
    //previewFileUrl 预览lims文件时传递
    //previewFileUrl 如果文件只允许预览，不允许下载，则传 true
    async init(fileId, fileName, previewFileUrl, canNotDownload) {

      this.fileId = fileId
      this.fileName = fileName
      this.previewFileUrl = previewFileUrl
      this.canNotDownload = canNotDownload

      //PBI文件
      if (this.fileId) {
        sysFileInfoDetail({id: this.fileId}).then(res => {
          this.fileInfo = res.data
          this.fileSuffix = res.data.fileSuffix
          if (this.isPdf(this.fileSuffix) ) {
            // 根据设备像素密度调整缩放比例
            // this.scale = window.devicePixelRatio*50 || 10
            this.previewPdf(this.fileId)
          }else if (this.isImg(this.fileSuffix) || this.isTXT(this.fileSuffix)) {
            this.previewUrl = '/api/sysFileInfo/preview?Authorization=Bearer ' + Vue.ls.get("Access-Token") + '&id=' + this.fileId + "#toolbar=0"
            this.filePreviewVisible = true
          } else if (this.isMp4(this.fileSuffix)) {
            //视频直接从minio预览
            getMinioPreviewUrl(this.fileId).then(res => {
              this.previewUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/")
              const videoPlayer = this.$refs.videoPlayer;
              if (videoPlayer) {
                videoPlayer.load();
              }
            }).then(() => this.filePreviewVisible = true)
            //转换后预览
          }else if(this.canConvert(this.fileSuffix) && res.data.canConvert != 0){
            // this.filePreviewVisible = true
            // this.spinning = true
            if(res.data.previewId){
              this.previewPdf(res.data.previewId)
              // this.spinning = false
              return
            }else{
              //转换不了
              if(res.data.canConvert == 0){
                getMinioDownloadUrl(this.fileId,this.fileName).then(res1 => {
                  downloadMinioFile(res1.data)
                }).finally(() => {
                  // this.spinning = false
                  this.filePreviewVisible = false
                })
              }else{
                this.$message.warn('文件预览处理中，请稍后重试')
                return

                //尝试1次
                /*convertToPdf({fileId:this.fileId}).then(res => {
                  this.convertNum++
                  this.init(fileId, fileName, previewFileUrl, canNotDownload)
                })*/
              }

            }

          } else {
            if(canNotDownload){
              this.$message.warn('文件暂不支持下载')
            }else{
              //直接下载
              getMinioDownloadUrl(this.fileId,this.fileName).then(res1 => {
                downloadMinioFile(res1.data)
              })
            }
          }
        })
        //预览lims文件
      }else if(previewFileUrl){
        this.limsPreview(previewFileUrl)
      }
    },

    // 使用防抖函数包装
    limsPreview: debounce(function(previewFileUrl) {
      // 判断是否是PDF文件
      const isPdf = /\.pdf$/i.test(previewFileUrl);

      if (isPdf) {
        this.handlePdfPreview(previewFileUrl);
      } else {
        this.handleFileDownload(previewFileUrl);
      }
    }, 300), // 300ms防抖时间

    // 处理PDF预览
    async handlePdfPreview(previewFileUrl) {
      try {
        this.fileSuffix = "pdf";
        this.filePreviewVisible = false; // 先关闭再打开，确保重新渲染
        this.previewUrl = previewFileUrl.replace("http://lims.evebattery.com/", "/limsPreview/");

      /*  // 销毁之前的加载任务
        if (this.loadingTask) {
          try {
            this.loadingTask.destroy();
          } catch (e) {
            console.warn('销毁PDF任务时出错:', e);
          }
        }

        // 创建新的加载任务
        this.loadingTask = pdf.createLoadingTask(this.previewUrl);
        const pdfDoc = await this.loadingTask.promise;

        this.numPages = pdfDoc.numPages;
        this.pdfDocument = this.loadingTask;*/
        this.filePreviewVisible = true;
      } catch (error) {
        console.error('PDF预览出错:', error);
        // 可以添加错误提示
      }
    },

    // 处理文件下载
    handleFileDownload(previewFileUrl) {
      const link = document.createElement('a');
      link.href = previewFileUrl.replace("http://lims.evebattery.com/", "/limsPreview/");
      link.setAttribute('download', this.fileName);
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /**
     *
     * @param fileId pbi文件
     * @param fileName 需要重命名的可以传递
     * @param fileUrl lims文件
     */
    async downloadFile(fileId,fileName,fileUrl){
      if(fileId){

        // 获取临时下载地址
        const res1 = await getMinioDownloadUrl(fileId, fileName);

        // 触发下载，并等待一小段时间
        downloadMinioFile(res1.data);

        // 延迟一段时间再继续下一个下载（关键！）
        await new Promise(resolve => setTimeout(resolve, 1000));

      } else if(fileUrl){
        const link = document.createElement('a');
        link.href = fileUrl.replace("http://lims.evebattery.com/", "/limsPreview/");
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
      }
    },

    previewPdf: debounce(async function(fileId) {
      try {
        // 显示加载状态
        this.loading = true;

        // 先关闭预览窗口（如果有打开的）
        this.filePreviewVisible = false;

        // 获取Minio预览URL
        const res = await getMinioPreviewUrl(fileId);
        this.previewUrl = res.data.replace("http://10.100.1.99:9000/", "/minioDownload/");

        /*// 销毁之前的PDF加载任务（如果存在）
        if (this.loadingTask) {
          try {
            this.loadingTask.destroy();
          } catch (e) {
            console.warn('销毁PDF任务时出错:', e);
          }
        }

        // 创建新的PDF加载任务
        this.loadingTask = pdf.createLoadingTask(this.previewUrl);

        // 加载PDF
        const pdfDoc = await this.loadingTask.promise;
        this.numPages = pdfDoc.numPages;
        this.pdfDocument = this.loadingTask;*/
        this.fileSuffix = 'pdf';

        // 显示预览窗口
        this.filePreviewVisible = true;
      } catch (error) {
        console.error('PDF预览出错:', error);
        // 可以在这里添加错误处理逻辑
      } finally {
        this.loading = false;
      }
    }, 300),

    //右键空事件
    preventRightClick(event) {
      event.preventDefault();
    },
    handleClose() {
      this.fileId = null
      this.previewFileUrl = null
      this.numPages = 0
      this.pdfDocument = null
      this.filePreviewVisible = false
      this.$emit('close')
    },
    isPdf(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "pdf":
          return true;
        default:
          return false;
      }
    },
    isImg(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
        case "tiff":
          return true;
        default:
          return false;
      }
    },
    isPPT(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "ppt":
        case "pptx":
          return true;
        default:
          return false;
      }
    },
    isWord(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "doc":
        case "docx":
          return true;
        default:
          return false;
      }
    },
    isMp4(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "mp4":
        case "avi":
        case "mkv":
        case "mov":
        case "wmv":
        case "flv":
        case "webm":
          return true;
        default:
          return false;
      }
    },
    canConvert(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "doc":
        case "docx":

        /*case "xls":
        case "xlsx":*/

        case "ppt":
        case "pptx":
          return true;
        default:
          return false;
      }
    },
    isTXT(fileSuffix) {
      fileSuffix = fileSuffix.toLowerCase()
      switch (fileSuffix) {
        case "txt":
          return true;
        default:
          return false;
      }
    }
  }
}
</script>
<style scoped lang="less">
/deep/.pdf-container {
  width: 100%;
  overflow-x: auto;
}

/deep/.pdf-page {
  width: 100%;
  height: auto;
  border: 1px solid;
  margin-bottom: 10px; /* 页与页之间的间距 */
}
/deep/.ant-drawer-close {
  height: unset!important;
  line-height: unset!important;
}

</style>
