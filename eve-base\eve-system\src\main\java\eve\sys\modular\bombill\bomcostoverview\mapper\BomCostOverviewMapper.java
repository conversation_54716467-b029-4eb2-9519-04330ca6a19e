package eve.sys.modular.bombill.bomcostoverview.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import eve.sys.modular.bombill.bomcostoverview.entity.BomCostOverview;
import eve.sys.modular.bombill.scenario.entity.Scenario;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * BOM成本总览表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface BomCostOverviewMapper extends BaseMapper<BomCostOverview> {

    /**
     * 根据场景ID分组分页查询
     * 返回每个scenarioId的代表性场景记录，包含BOM成本总览信息
     *
     * @param page 分页参数
     * @param bomCostOverviewIds BOM成本总览ID列表
     * @param positiveElectrodeSystems 正极体系过滤条件
     * @return 分组后的场景分页结果
     */
    @Select({
        "<script>",
        "SELECT ",
        "  s.id,",
        "  s.scenario_id,",
        "  s.bom_cost_overview_id,",
        "  s.bom_accounting_detail_id,",
        "  s.untaxed_unit_price,",
        "  s.amount_cny_ea,",
        "  s.amount_cny_wh,",
        "  s.proportion,",
        "  s.base_use,",
        "  s.create_time,",
        "  s.update_time,",
        "  s.positive_material_accounting_id,",
        "  pma.chemical_system_code,",
        "  bco.accounting_code,",
        "  bco.accounting_date,",
        "  bco.customer_type,",
        "  bco.product_name,",
        "  bco.product_status,",
        "  bco.rated_capacity,",
        "  bco.rated_energy,",
        "  bco.bom_file_number,",
        "  bco.requester_name,",
        "  bco.issue_key,",
        "  bco.usage_description,",
        "  bco.audit_status,",
        "  bco.bom_file_id,",
        "  bco.accounting_type",
        "FROM (",
        "  SELECT ",
        "    bom_cost_overview_id,",
        "    scenario_id,",
        "    MIN(id) as min_id",
        "  FROM scenario s_inner",
        "  WHERE s_inner.delete_status = 0",
        "  AND s_inner.bom_cost_overview_id IN ",
        "  <foreach collection='bomCostOverviewIds' item='id' open='(' separator=',' close=')'>",
        "    #{id}",
        "  </foreach>",
        "  AND s_inner.positive_material_accounting_id IS NOT NULL",
        "  <if test='positiveElectrodeSystems != null and positiveElectrodeSystems.size() > 0'>",
        "    AND s_inner.positive_material_accounting_id IN ",
        "    <foreach collection='positiveElectrodeSystems' item='system' open='(' separator=',' close=')'>",
        "      #{system}",
        "    </foreach>",
        "  </if>",
        "  GROUP BY bom_cost_overview_id, scenario_id",
        ") grouped",
        "INNER JOIN scenario s ON s.id = grouped.min_id",
        "  AND s.bom_cost_overview_id = grouped.bom_cost_overview_id",
        "  AND s.scenario_id = grouped.scenario_id",
        "  AND s.delete_status = 0",
        "INNER JOIN bom_cost_overview bco ON bco.id = s.bom_cost_overview_id",
        "  AND bco.delete_status = 0",
        "LEFT JOIN positive_material_accounting pma ON pma.id = s.positive_material_accounting_id",
        "  AND pma.delete_status = 0",
        "</script>"
    })
    IPage<Scenario> selectScenarioGroupPageList(
            Page<Scenario> page,
            @Param("bomCostOverviewIds") List<Long> bomCostOverviewIds,
            @Param("positiveElectrodeSystems") List<Long> positiveElectrodeSystems
    );

    /**
     * 根据场景ID分组查询（非分页）
     * 返回每个scenarioId的代表性场景记录，包含BOM成本总览信息
     *
     * @param bomCostOverviewIds BOM成本总览ID列表
     * @param positiveElectrodeSystems 正极体系过滤条件
     * @return 分组后的场景列表
     */
    @Select({
        "<script>",
        "SELECT ",
        "  s.id,",
        "  s.scenario_id,",
        "  s.bom_cost_overview_id,",
        "  s.bom_accounting_detail_id,",
        "  s.untaxed_unit_price,",
        "  s.amount_cny_ea,",
        "  s.amount_cny_wh,",
        "  s.proportion,",
        "  s.base_use,",
        "  s.create_time,",
        "  s.update_time,",
        "  s.positive_material_accounting_id,",
        "  pma.chemical_system_code,",
        "  bco.accounting_code,",
        "  bco.accounting_date,",
        "  bco.customer_type,",
        "  bco.product_name,",
        "  bco.product_status,",
        "  bco.rated_capacity,",
        "  bco.rated_energy,",
        "  bco.bom_file_number,",
        "  bco.requester_name,",
        "  bco.audit_status,",
        "  bco.accounting_type",
        "FROM (",
        "  SELECT ",
        "    bom_cost_overview_id,",
        "    scenario_id,",
        "    MIN(id) as min_id",
        "  FROM scenario s_inner",
        "  WHERE s_inner.delete_status = 0",
        "  AND s_inner.bom_cost_overview_id IN ",
        "  <foreach collection='bomCostOverviewIds' item='id' open='(' separator=',' close=')'>",
        "    #{id}",
        "  </foreach>",
        "  AND s_inner.positive_material_accounting_id IS NOT NULL",
        "  <if test='positiveElectrodeSystems != null and positiveElectrodeSystems.size() > 0'>",
        "    AND s_inner.positive_material_accounting_id IN ",
        "    <foreach collection='positiveElectrodeSystems' item='system' open='(' separator=',' close=')'>",
        "      #{system}",
        "    </foreach>",
        "  </if>",
        "  GROUP BY bom_cost_overview_id, scenario_id",
        ") grouped",
        "INNER JOIN scenario s ON s.id = grouped.min_id",
        "  AND s.bom_cost_overview_id = grouped.bom_cost_overview_id",
        "  AND s.scenario_id = grouped.scenario_id",
        "  AND s.delete_status = 0",
        "INNER JOIN bom_cost_overview bco ON bco.id = s.bom_cost_overview_id",
        "  AND bco.delete_status = 0",
        "LEFT JOIN positive_material_accounting pma ON pma.id = s.positive_material_accounting_id",
        "  AND pma.delete_status = 0",
        "ORDER BY s.bom_cost_overview_id DESC, s.scenario_id ASC",
        "</script>"
    })
    List<Scenario> selectScenarioGroupList(
            @Param("bomCostOverviewIds") List<Long> bomCostOverviewIds,
            @Param("positiveElectrodeSystems") List<Long> positiveElectrodeSystems
    );
}
