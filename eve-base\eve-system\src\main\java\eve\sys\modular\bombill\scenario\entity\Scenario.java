package eve.sys.modular.bombill.scenario.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;

import eve.core.pojo.base.entity.BaseEntity;
import lombok.*;

/**
 * 场景实体类
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("SCENARIO")
@Builder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
public class Scenario extends BaseEntity {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonProperty("id")
    private Long id;

    /**
     * 正极材料核算表ID（替代原chemicalSystemCode）
     */
    private Long positiveMaterialAccountingId;

    /**
     * 化学体系编号（用于显示，从PositiveMaterialAccounting关联查询）
     */
    @TableField(exist = false)
    private String chemicalSystemCode;

    /**
     * 化学体系名称（用于显示，从PositiveMaterialAccounting关联查询）
     */
    @TableField(exist = false)
    private String chemicalSystem;

    /* 场景ID */
    private Long scenarioId;

    /**
     * BOM成本总览表ID
     */
    private Long bomCostOverviewId;

    /**
     * BOM核算明细表ID
     */
    private Long bomAccountingDetailId;

    /**
     * 未税单价
     */
    @JsonProperty("untaxedUnitPrice")
    private BigDecimal untaxedUnitPrice;

    /**
     * 金额(CNY/EA)
     */
    private BigDecimal amountCnyEa;

    /**
     * 金额(CNY/Wh)
     */
    private BigDecimal amountCnyWh;

    /**
     * 占比
     */
    private BigDecimal proportion;

    @JsonProperty("baseUse")
    private BigDecimal baseUse;

    /* 存储id和未税价 */
    @TableField(exist = false)
    @JsonProperty("scenarios")
    private List<Scenario> scenarios;

    // ========== BOM成本总览相关字段（用于分组查询时返回） ==========

    /**
     * 核算代码
     */
    @TableField(exist = false)
    private String accountingCode;

    /**
     * 核算日期
     */
    @TableField(exist = false)
    private Date accountingDate;

    /**
     * 客户类型
     */
    @TableField(exist = false)
    private Integer customerType;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 产品状态
     */
    @TableField(exist = false)
    private Integer productStatus;

    /**
     * 额定容量
     */
    @TableField(exist = false)
    private BigDecimal ratedCapacity;

    /**
     * 额定能量
     */
    @TableField(exist = false)
    private BigDecimal ratedEnergy;

    /**
     * BOM文件编号
     */
    @TableField(exist = false)
    private String bomFileNumber;

    /**
     * 需求人
     */
    @TableField(exist = false)
    private String requesterName;

    /**
     * 审核状态
     */
    @TableField(exist = false)
    private Integer auditStatus;

    /**
     * 核算类型
     */
    @TableField(exist = false)
    private Integer accountingType;

    /* 
     * 化学材料价格总计
     */
    @TableField(exist = false)
    private BigDecimal chemicalElementPriceTotal;

    /* 
     * 结构件价格总计
     */
    @TableField(exist = false)
    private BigDecimal structurePriceTotal;

    /* 
     * 价格总计
     */
    @TableField(exist = false)
    private BigDecimal priceTotal;


    /**
     * BOM文件ID
     */
    @TableField(exist = false)
    private Long bomFileId;

    @TableField(exist = false)
    private String usageDescription;

    @TableField(exist = false)
    private String issueKey;
}
