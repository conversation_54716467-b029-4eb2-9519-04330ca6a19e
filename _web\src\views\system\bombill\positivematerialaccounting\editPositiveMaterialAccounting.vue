<template>
  <a-modal
    centered
    title="编辑正极材料核算"
    :visible="visible"
    :width="1000"
    :loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <a-form :form="form" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item style="display: none">
          <a-input v-decorator="['id']" />
        </a-form-item>
        
        <!-- 基本信息 -->
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="正极体系" required>
              <a-input
                v-decorator="['chemicalSystem', {
                  rules: [{ required: true, message: '请输入正极体系！' }]
                }]"
                placeholder="请输入正极体系"
              />
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="加工费" required>
              <a-input-number
                v-decorator="['processingFee']"
                placeholder="请输入加工费"
                :min="0"
                :precision="3"
                :step="0.001"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 金属价元素价格维护 -->
        <a-divider orientation="left">
          <span style="font-weight: bold; ">金属价元素价格维护</span>
        </a-divider>
        <a-row :gutter="24" v-if="metalPriceElements.length">
          <a-col :span="8" v-for="(item, i) in metalPriceElements" :key="`metal_${i}`">
            <a-form-item :label="item.elementName">
              <a-input-number
                v-decorator="[`${item.accountingType}_${item.elementName}`]"
                :placeholder="item.placeholder"
                :min="0"
                :precision="3"
                :step="0.001"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div v-else style="text-align: center; color: #999; padding: 20px;">
          暂无金属价元素数据
        </div>

        <!-- 盐价元素价格维护 -->
        <a-divider orientation="left">
          <span style="font-weight: bold; ">盐价元素价格维护</span>
        </a-divider>
        <a-row :gutter="24" v-if="saltPriceElements.length">
          <a-col :span="8" v-for="(item, i) in saltPriceElements" :key="`salt_${i}`">
            <a-form-item :label="item.elementName">
              <a-input-number
                v-decorator="[`${item.accountingType}_${item.elementName}`]"
                :placeholder="item.placeholder"
                :min="0"
                :precision="3"
                :step="0.001"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div v-else style="text-align: center; color: #999; padding: 20px;">
          暂无盐价元素数据
        </div>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script>
import { positiveMaterialAccountingEdit } from '@/api/modular/system/positiveMaterialAccountingManage'
import { getChemicalElementList } from "@/api/modular/system/chemicalElementManage"
import Vue from 'vue'
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
export default {
  data() {
    return {
      visible: false,
      loading: false,
      chemicalCols: [],
      form: this.$form.createForm(this)
    }
  },

  computed: {
    // 金属价元素（核算类型为1）
    metalPriceElements() {
      return this.chemicalCols.filter(item => item.accountingType === '1')
    },

    // 盐价元素（核算类型为2）
    saltPriceElements() {
      return this.chemicalCols.filter(item => item.accountingType === '2')
    }
  },
  
  methods: {
    getDictName(code,key) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			let dict = dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
			let name = dict.find(item=>item.code == key)?.name
			return name ?? '-'
		},

    getChemicalElementList(record) {
			this.loading = true
			getChemicalElementList({}).then(res => {
				if (res.success) {
          for (const e of res.data) {
            e.placeholder = `每KG正极${this.getDictName('bom_bill_account_type',e.accountingType)}用量(kg)`
          }
          this.chemicalCols = res.data
          
          const params = {
              id: record.id,
              chemicalSystem: record.chemicalSystem,
              processingFee: record.processingFee
          }
          for (const e of res.data) {
            params[`${e.accountingType}_${e.elementName}`] = record[`${e.accountingType}_${e.elementName}`]
          }

          setTimeout(() => {
            this.$nextTick(() => {
              this.form.setFieldsValue(params)
            })
          }, 200);
          
				}
			}).finally(() => {
				this.loading = false
			})
		},
    edit(record) {
      this.getChemicalElementList(record)
      this.visible = true
    },
    
    handleSubmit() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          this.loading = true

          const params = {}
          for (const e of this.chemicalCols) {
            params[`${e.accountingType}_${e.elementName}`] = values[`${e.accountingType}_${e.elementName}`]
          }
          values.chemicalManagementElementsJson = JSON.stringify(params)

          positiveMaterialAccountingEdit(values).then(res => {
            if (res.success) {
              this.$message.success('更新成功')
              this.visible = false
              this.$emit('ok')
            } else {
              this.$message.error('更新失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    
    handleCancel() {
      this.form.resetFields()
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-modal-footer{
 padding:0 24px 24px; 
}
.link-btn {
  margin-right: 8px;
  color: #1890FF;
}
</style>