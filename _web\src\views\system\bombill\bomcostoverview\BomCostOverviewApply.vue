<template>
  <div class="bom-apply-container">
    <a-modal
      centered
      title="BOM成本核算申请"
      :visible="visible"
      :loading="loading"
      @ok="handleSubmit"
      @cancel="handleCancel"
      width="1000px"
      :maskClosable="false"
    >
      <template #footer>
        <div class="footer-buttons">
          <a-button :style="{float:'left'}" type="link" @click="go">
            {{ isCustomMode ? '点击此处，选择已有的产品或BOM' : '未找到相关产品或相关BOM，点击此处，修改为自定义产品、BOM' }}
          </a-button>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" :loading="loading" @click="handleSubmit">提交申请</a-button>
          <a-button v-if="hasPerm('bomCostOverview:add')" type="primary" :loading="loading" @click="create">新建</a-button>
        </div>
      </template>
      
      <a-spin :spinning="loading">
        <a-form :form="form" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
          <a-row :gutter="24">
            <!-- 需求人 -->
            <a-col :span="12">
              <a-form-item label="需求人" required>
                <a-select
                  v-decorator="['requesterJobNumber', {
                    rules: [{ required: true, message: '请选择需求人！' }]
                  }]"
                  @change="handleChange"
                  @search="handleSearch"
                  allowClear
                  showSearch
                  placeholder="请输入需求人姓名搜索"
                  :filterOption="false"
                >
                  <a-select-option
                    v-for="user in userList"
                    :key="user.account"
                    :value="user.account"
                  >
                    {{ user.name }}
                  </a-select-option>
                </a-select>
                <a-input
                  v-decorator="['requesterName']"
                  style="display: none;"
                />
              </a-form-item>
            </a-col>

            <!-- 部门长 -->
            <a-col :span="12">
              <a-form-item label="部门长" required>
                <a-select
                  v-decorator="['majordomo', {
                    rules: [{ required: true, message: '请选择部门长！' }]
                  }]"
                  @change="handleMajordomoChange"
                  @search="handleMajordomoSearch"
                  allowClear
                  showSearch
                  placeholder="请输入部门长姓名搜索"
                  :filterOption="false"
                >
                  <a-select-option
                    v-for="user in majordomoList"
                    :key="user.account"
                    :value="user.account"
                  >
                    {{ user.name }}
                  </a-select-option>
                </a-select>
                <a-input
                  v-decorator="['majordomoName']"
                  style="display: none;"
                />
              </a-form-item>
            </a-col>

            <!-- 研究所 -->
            <a-col :span="12">
              <a-form-item label="研究所" required>
                <a-select
                  v-decorator="['dept', {
                    rules: [{ required: true, message: '请选择研究所！' }]
                  }]"
                  placeholder="请选择研究所"
                >
                  <a-select-option v-for="(item,i) in getDict('bom_bill_depts')" :value="item.code" :key="i">
                      {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- 客户类型 -->
            <a-col :span="12">
              <a-form-item label="客户类型" required>
                <a-select
                  v-decorator="['customerType', {
                    rules: [{ required: true, message: '请选择客户类型！' }]
                  }]"
                  placeholder="请选择客户类型"
                >
                  <a-select-option v-for="(item,i) in getDict('bom_bill_customer_type')" :value="item.code" :key="i">
                      {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <!-- 产品名称 -->
            <a-col :span="12">
              <a-form-item label="产品" required>
                <!-- 选择已有产品模式 -->
                <a-select
                  v-if="!isCustomMode"
                  option-filter-prop="children"
                  v-decorator="['issueId', {
                    rules: [{ required: true, message: '请选择产品！' }]
                  }]"
                  placeholder="请选择产品"
                  allowClear
                  show-search
                  @change="handleProductChange"
                >
                  <a-select-option
                    v-for="item in products"
                    :key="item.issueId"
                    :value="item.issueId"
                  >
                    {{ item.productName }}
                  </a-select-option>
                </a-select>
                <!-- 自定义产品模式 -->
                <a-input
                  v-if="isCustomMode"
                  v-decorator="['productName', {
                    rules: [{ required: true, message: '请输入产品名称！' }]
                  }]"
                  placeholder="请输入产品名称"
                />
                <!-- 隐藏字段 -->
                <a-input
                  v-decorator="['productName']"
                  v-if="!isCustomMode"
                  style="display: none;"
                />
                <a-input
                  v-decorator="['issueId']"
                  v-if="isCustomMode"
                  style="display: none;"
                />
              </a-form-item>
            </a-col>

            <!-- 产品状态 -->
            <a-col :span="12">
              <a-form-item label="产品状态" required>
                <a-select
                  v-decorator="['productStatus', {
                    rules: [{ required: true, message: '请选择产品状态！' }]
                  }]"
                  placeholder="请选择产品状态"
                >
                  <a-select-option v-for="(item,i) in getDict('product_state_status')" :value="item.code" :key="i">
                      {{ item.name }}
                  </a-select-option>
                  
                </a-select>
              </a-form-item>
            </a-col>

            <!-- 额定容量 -->
            <a-col :span="12">
              <a-form-item label="额定容量">
                <a-input-number
                  v-decorator="['ratedCapacity',{
                    rules: [{ required: true, message: '请输入额定容量!' }]
                  }]"
                  placeholder="请输入额定容量"
                  style="width: 100%"
                  :precision="3"
                  :min="0.001"
                  :step="0.001"
                  addon-after="Ah"
                />
              </a-form-item>
            </a-col>

            <!-- 额定电压 -->
            <a-col :span="12">
              <a-form-item label="额定电压">
                <a-input-number
                  v-decorator="['ratedVoltage',{
                    rules: [{ required: true, message: '请输入额定电压!' }]
                  }]"
                  placeholder="请输入额定电压"
                  style="width: 100%"
                  :precision="3"
                  :min="0.001"
                  :step="0.001"
                  addon-after="V"
                />
              </a-form-item>
            </a-col>

            <!-- 额定能量 -->
            <!-- <a-col :span="12">
              <a-form-item label="额定能量">
                <a-input-number
                  v-decorator="['ratedEnergy']"
                  placeholder="请输入额定能量"
                  style="width: 100%"
                  :precision="2"
                  :min="0"
                  addon-after="Wh"
                />
              </a-form-item>
            </a-col> -->

            <!-- BOM文件编号 -->
            <a-col :span="12">
              <a-form-item label="BOM">
                <!-- 选择已有BOM模式 -->
                <a-select
                  v-if="!isCustomMode"
                  v-decorator="['bomId',{
                    rules: [{ required: true, message: '请选择BOM文件编号!' }]
                  }]"
                  placeholder="请选择BOM文件编号"
                  allowClear
                  show-search
                  option-filter-prop="children"
                  @change="handleBomFileChange"
                >
                  <a-select-option
                    v-for="bomFile in bomFiles"
                    :key="bomFile.id"
                    :value="bomFile.id"
                  >
                    {{ bomFile.bomNo }}
                  </a-select-option>
                </a-select>
                <!-- 自定义BOM模式 -->
                <a-input
                  v-if="isCustomMode"
                  v-decorator="['bomFileNumber']"
                  placeholder="请输入BOM文件编号"
                />
                <!-- 隐藏字段 -->
                <a-input
                  v-decorator="['bomFileNumber']"
                  v-if="!isCustomMode"
                  style="display: none;"
                />
                <a-input
                  v-decorator="['bomId']"
                  v-if="isCustomMode"
                  style="display: none;"
                />
              </a-form-item>
            </a-col>

            <!-- BOM文件ID -->
            <a-col :span="12">
              <a-form-item label="BOM文件">
                <div style="display: flex; gap: 8px; align-items: flex-start;">
                  <a-upload
                    v-decorator="['bomFileId', isCustomMode ? {
                      rules: [{ required: true, message: '请上传BOM文件!' }]
                    } : {}]"
                    :remove="removeFile"
                    name="file"
                    :file-list="fileList"
                    :customRequest="customRequest"
                    accept=".xlsx,.xls"
                    :multiple="false"
                  >
                    <a-button>
                      <a-icon type="upload" /> 选择Excel文件
                    </a-button>
                  </a-upload>
                  <a-button
                    type="link"
                    @click="downloadTemplate"
                    :loading="templateDownloading"
                    style="padding: 4px 8px; height: auto;"
                  >
                    <a-icon type="download" /> 下载模板
                  </a-button>
                </div>
              </a-form-item>
            </a-col>

            <!-- 用途说明 -->
            <a-col :span="12">
              <a-form-item label="用途说明">
                <a-textarea
                  v-decorator="['usageDescription',{
                    rules: [{ required: true, message: '请填写用途说明!' }]
                  }]"
                  placeholder="请详细描述产品用途"
                  :rows="2"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 正极体系 -->
          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item>
                <template slot="label">
                  <span>正极体系</span>
                  <span style="color: #ff4d4f; margin-left: 4px;">*</span>
                  
                </template>
                <div class="dynamic-form-container">
                  <div
                    v-for="(item, index) in aNodesForms"
                    :key="item.key"
                    class="dynamic-form-item"
                  >
                    <a-row :gutter="8" type="flex" align="middle">
                      <a-col :span="8">
                        <a-select
                          v-model="item.type"
                          placeholder="请选择正极体系"
                          @change="handleAnodeChange(index, $event)"
                          :class="{ 'error-border': item.showError && !item.type }"
                        >
                          <a-select-option v-for="aNode in aNodeTypes" :key="aNode.id" :value="aNode.id">
                            {{ aNode.chemicalSystem }}
                          </a-select-option>

                          
                        </a-select>
                        <div v-if="item.showError && !item.type" class="error-message">
                          请选择正极体系
                        </div>
                      </a-col>
                      <a-col :span="14">
                        <a-input
                          v-model="item.value"
                          placeholder="请输入物料号"
                          @change="handleNodeChange(index, $event)"
                          :class="{ 'error-border': item.showError && !item.value }"
                        />
                        <div v-if="item.showError && !item.value" class="error-message">
                          请输入物料号
                        </div>
                      </a-col>
                      <a-col :span="2">
                        <a-button
                          type="danger"
                          icon="minus"
                          size="small"
                          @click="removeNode(index)"
                          :disabled="aNodesForms.length <= 1"
                        />
                      </a-col>
                    </a-row>
                  </div>

                  <a-button
                    type="dashed"
                    icon="plus"
                    @click="addNode"
                    style="width: 100%; margin-top: 8px;"
                  >
                    添加正极体系
                  </a-button>
                </div>
                <!-- 隐藏的动态表单数据字段 -->
                <a-input
                  v-decorator="['aNodeItems']"
                  style="display: none;"
                />
              </a-form-item>
            </a-col>
          </a-row>

        </a-form>
      </a-spin>
    </a-modal>
  </div>
</template>

<script>
import { bomCostOverviewAdd } from '@/api/modular/system/bomCostOverviewManage'
import { getUserLists } from "@/api/modular/system/userManage"
import { DICT_TYPE_TREE_DATA } from '@/store/mutation-types'
import { getAlls } from "@/api/modular/system/productManage"
import { getBomListForSelect } from "@/api/modular/system/bomManage"
import { sysFileInfoUpload } from '@/api/modular/system/fileManage'
import { getPositiveMaterialAccountingList } from "@/api/modular/system/positiveMaterialAccountingManage"
import { downloadBomTemplate } from '@/api/modular/system/bomAccountingDetailManage'
import Vue from 'vue'
export default {
  name: 'BomCostOverviewApply',
  
  data() {
    return {
      visible: false,
      loading: false,
      templateDownloading: false,
      userList: [],
      majordomoList: [],
      products: [],
      bomFiles: [],
      aNodeTypes: [],
      form: this.$form.createForm(this),
      fileList: [],
      aNodesForms: [],
      aNodeKey: Date.now(),
      isCustomMode: false, // 控制是否为自定义模式
    }
  },
  
  methods: {
    getPositiveMaterialAccountingList(){
      getPositiveMaterialAccountingList({}).then(res => {
        const _data = res?.data.filter(item => item.chemicalSystemCode.indexOf('001') != -1)
        this.aNodeTypes = _data
      })
    },
    handleProductChange(val) {

      this.form.setFieldsValue({
          productName: undefined,
          productStatus: undefined,
          bomId: undefined,
          bomFileNumber: undefined
      })

      const product = this.products.find(item => item.issueId == val)
      if (product) {
        // 更新产品名称到表单
        this.form.setFieldsValue({
          productName: product.productName,
          productStatus: (product.productState || 1) + ''
        })

        this.getBomFileList(val)
      }
    },
    getProductList(){
      getAlls({}).then(res => {
        this.products = res?.data.filter(filterItem => filterItem.productOrProject == 1) || []
      })
    },

    // 获取BOM文件列表
    getBomFileList(val) {
      getBomListForSelect({bomIssueId:val}).then(res => {
        this.bomFiles = res?.data.filter(item => item.bomNo) || []
      })
    },

    // 处理BOM文件选择
    handleBomFileChange(val) {
      const bomFile = this.bomFiles.find(item => item.id === val)
      if (bomFile) {
        this.form.setFieldsValue({
          bomFileNumber: bomFile.bomNo
        })
      }
    },

    getDict(code) {
			const dictTypeTree = Vue.ls.get(DICT_TYPE_TREE_DATA)
			return dictTypeTree ? dictTypeTree.filter(item => item.code == code)[0].children : []
		},
    handleChange(value) {
      this.form.setFieldsValue({
        requesterName: this.userList.find(item => item.account === value)?.name || '-'
      })
		},
    handleSearch(searchValue) {
      if (searchValue && searchValue.trim()) {
          const param = { searchValue: searchValue.trim() }
          getUserLists(param).then(res => {
            this.userList = res?.data?.rows || []
          })
        }
    },
    // 部门长选择变化处理
    handleMajordomoChange(value) {
      this.form.setFieldsValue({
        majordomoName: this.majordomoList.find(item => item.account === value)?.name || '-'
      })
    },
    // 部门长搜索处理
    handleMajordomoSearch(searchValue) {
      if (searchValue && searchValue.trim()) {
          const param = { searchValue: searchValue.trim() }
          getUserLists(param).then(res => {
            this.majordomoList = res?.data?.rows || []
          })
        }
    },
    // 显示申请弹窗
    show(customMode = false) {
      this.isCustomMode = customMode

      if (!this.isCustomMode) {
        this.getProductList()
      }

      this.getPositiveMaterialAccountingList()

      this.aNodeKey = Date.now()

      this.$nextTick(() => {
        this.form.resetFields()
        this.aNodesForms = [{
          key: this.aNodeKey,
          type: undefined,
          value: undefined,
          showError: false
        }]
        // 设置默认值
        if (this.isCustomMode) {
          this.form.setFieldsValue({
            issueId: 0,
            bomId: 0
          })
        }
      })

      this.visible = true
    },

    // 提交申请
    handleSubmit() {
      this.form.validateFields((errors) => {
        if (!errors) {
          // 验证动态表单项
          if (!this.validateNodes()) {
            this.$message.error('请完整填写所有正极体系')
            return
          }

          // 获取表单值
          const formValues = this.form.getFieldsValue()

          // 直接从aNodesForms构建aNodeItems数据
          // 注意：type字段现在传递的是PositiveMaterialAccounting的主键ID
          const aNodeItems = this.aNodesForms
            .filter(item => item.type && item.value)
            .map(item => ({
              type: item.type, // 现在是PositiveMaterialAccounting的ID
              value: item.value
            }))

          // 添加调试日志
          console.log('表单值:', formValues)
          console.log('构建的aNodeItems:', aNodeItems)
          console.log('原始aNodesForms:', this.aNodesForms)

          this.loading = true

          const submitData = {
            ...formValues,
            aNodeItems: aNodeItems  // 直接设置aNodeItems
            /* accountingDate: moment().format('YYYY-MM-DD'),
            auditStatus: 0, // 待审核 */
          }

          console.log('最终提交数据:', submitData)
          bomCostOverviewAdd(submitData).then(res => {
            if (res.success) {
              this.$message.success('申请提交成功，请等待审核')
              this.visible = false
              this.$emit('success', res.data)
            } else {
              this.$message.error('申请提交失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    create() {
      this.form.validateFields((errors, values) => {
        if (!errors) {
          // 验证动态表单项
          if (!this.validateNodes()) {
            this.$message.error('请完整填写所有正极体系')
            return
          }

          this.loading = true

          const submitData = {
            ...values ,
            auditStatus: 1, 
          }

          bomCostOverviewAdd(submitData).then(res => {
            if (res.success) {
              this.$message.success('申请提交成功，请等待审核')
              this.visible = false
              this.$emit('success', res.data)
            } else {
              this.$message.error('申请提交失败：' + res.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },

    // 取消申请
    handleCancel() {
      this.visible = false
      this.form.resetFields()
      this.aNodesForms = []
      this.fileList = []
      this.isCustomMode = false // 重置为默认模式
    },

    go(){
      // 切换模式时只重置模式相关的字段，保留其他已填写的值
      const currentFormValues = this.form.getFieldsValue()

      // 切换模式
      this.isCustomMode = !this.isCustomMode

      // 重置模式相关的字段
      if (this.isCustomMode) {
        // 切换到自定义模式：清空产品选择，保留产品名称输入
        this.form.setFieldsValue({
          issueId: 0,
          bomId: 0,
          bomFileNumber: currentFormValues.productName || undefined,
          productName: currentFormValues.productName || undefined
        })
        // 清空产品和BOM列表相关数据
        this.products = []
        this.bomFiles = []
      } else {
        // 切换到选择模式：清空自定义输入，重新加载产品列表
        this.form.setFieldsValue({
          issueId: undefined,
          productName: undefined,
          bomId: undefined,
          bomFileNumber: undefined
        })
        // 重新加载产品列表
        this.getProductList()
      }

      // 触发父组件的模式切换事件（如果需要）
      this.$emit('show')
    },

    // 动态表单项相关方法
    addNode() {
      // 验证当前所有项是否都已填写
      let canAdd = true
      this.aNodesForms.forEach(item => {
        if (!item.type || !item.value) {
          item.showError = true
          canAdd = false
        }
      })

      if (!canAdd) {
        this.$message.warning('请先完整填写当前的附加信息项')
        return
      }

      this.aNodeKey++
      this.aNodesForms.push({
        key: this.aNodeKey,
        type: undefined,
        value: undefined,
        showError: false
      })
    },

    removeNode(index) {
      if (this.aNodesForms.length > 1) {
        this.aNodesForms.splice(index, 1)
        this.updateNodeData()
      }
    },

    handleAnodeChange(index, value) {
      this.aNodesForms[index].type = value
      this.aNodesForms[index].showError = false // 清除错误状态
      this.updateNodeData()
    },

    handleNodeChange(index, event) {
      this.aNodesForms[index].value = event.target.value
      this.aNodesForms[index].showError = false // 清除错误状态
      this.updateNodeData()
    },

    // 验证动态表单项
    validateNodes() {
      let isValid = true
      this.aNodesForms.forEach(item => {
        if (!item.type || !item.value) {
          item.showError = true
          isValid = false
        } else {
          item.showError = false
        }
      })
      return isValid
    },

    updateNodeData() {
      // 将动态表单项数据转换为提交格式
      // 注意：type字段现在传递的是PositiveMaterialAccounting的主键ID
      const _datas = this.aNodesForms
        .filter(item => item.type && item.value)
        .map(item => ({
          type: item.type, // 现在是PositiveMaterialAccounting的ID
          value: item.value
        }))

      // 可以将数据存储到表单中或直接在提交时使用
      this.form.setFieldsValue({
        aNodeItems: _datas
      })
    },

    removeFile(){
      this.form.setFieldsValue({
          bomFileId: undefined
      })
      this.fileList = []
    },

    // 下载BOM模板
    downloadTemplate() {
      this.templateDownloading = true
      downloadBomTemplate({}).then(res => {
        // 生成文件名
        const now = new Date()
        const timestamp = now.getFullYear() +
          String(now.getMonth() + 1).padStart(2, '0') +
          String(now.getDate()).padStart(2, '0') + '_' +
          String(now.getHours()).padStart(2, '0') +
          String(now.getMinutes()).padStart(2, '0') +
          String(now.getSeconds()).padStart(2, '0')

        const fileName = `BOM文件模板_${timestamp}.xlsx`
        const _res = res.data
        let blob = new Blob([_res])
        let downloadElement = document.createElement("a")
        //创建下载的链接
        let href = window.URL.createObjectURL(blob)
        downloadElement.href = href
        //下载后文件名
        downloadElement.download = fileName
        document.body.appendChild(downloadElement)
        //点击下载
        downloadElement.click()
        //下载完成移除元素
        document.body.removeChild(downloadElement)
        //释放掉blob对象
        window.URL.revokeObjectURL(href)
        this.$message.success('模板下载成功')
      }).catch(error => {
        console.error('模板下载失败:', error)
        this.$message.error('模板下载失败，请重试')
      }).finally(() => {
        this.templateDownloading = false
      })
    },
    // 自定义上传
    customRequest (data) {
        const formData = new FormData()
        formData.append('file', data.file)
        sysFileInfoUpload(formData).then((res) => {
          if (res.success) {
            this.form.setFieldsValue({
                bomFileId: res.data
            })
            this.fileList = [{
                uid: data.file.uid,
                name: data.file.name,
                status: 'done',
            }]
          }
        })
      },

  },
  
}
</script>

<style scoped>
.dynamic-form-container {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafafa;
}

.dynamic-form-item {
  margin-bottom: 8px;
}

.dynamic-form-item:last-child {
  margin-bottom: 0;
}

.dynamic-form-item .ant-row {
  align-items: center;
}

.dynamic-form-item .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 错误状态样式 */
.error-border .ant-select-selector,
.error-border.ant-input {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2) !important;
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
}

/* 必填标识 */
.dynamic-form-container .ant-form-item-label::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}
</style>

<style lang="less" scoped=''>
.bom-apply-container {
  
  .apply-notice {
    margin: 20px 0;
  }

  .related-links {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
    
    a {
      color: #1890ff;
      margin: 0 8px;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .footer-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}

/deep/ .ant-form-item-label {
  font-weight: 500;
}

/deep/ .ant-input-number-handler-wrap {
  opacity: 1;
}
</style>
